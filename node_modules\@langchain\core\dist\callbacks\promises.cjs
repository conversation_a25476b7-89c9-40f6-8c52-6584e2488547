"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.consumeCallback = exports.awaitAllCallbacks = void 0;
const callbacks_js_1 = require("../singletons/callbacks.cjs");
Object.defineProperty(exports, "awaitAllCallbacks", { enumerable: true, get: function () { return callbacks_js_1.awaitAllCallbacks; } });
Object.defineProperty(exports, "consumeCallback", { enumerable: true, get: function () { return callbacks_js_1.consumeCallback; } });
