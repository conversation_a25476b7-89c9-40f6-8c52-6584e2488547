import { LangChainTracer } from "./tracer_langchain.js";
import { LangChainTracerV1 } from "./tracer_langchain_v1.js";
/**
 * @deprecated Use the V2 handler instead.
 *
 * Function that returns an instance of `LangChainTracerV1`. If a session
 * is provided, it loads that session into the tracer; otherwise, it loads
 * a default session.
 * @param session Optional session to load into the tracer.
 * @returns An instance of `LangChainTracerV1`.
 */
export declare function getTracingCallbackHandler(session?: string): Promise<LangChainTracerV1>;
/**
 * @deprecated Instantiate directly using the LangChainTracer constructor.
 *
 * Function that returns an instance of `LangChainTracer`. It does not
 * load any session data.
 * @returns An instance of `LangChainTracer`.
 */
export declare function getTracingV2CallbackHandler(): Promise<LangChainTracer>;
