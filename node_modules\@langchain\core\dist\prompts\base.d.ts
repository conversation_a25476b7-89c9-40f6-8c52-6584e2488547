import type { InputValues, PartialValues, StringWithAutocomplete } from "../utils/types/index.js";
import { type BasePromptValueInterface } from "../prompt_values.js";
import { BaseOutputParser } from "../output_parsers/index.js";
import type { SerializedFields } from "../load/map_keys.js";
import { Runnable } from "../runnables/base.js";
import { BaseCallbackConfig } from "../callbacks/manager.js";
import type { SerializedBasePromptTemplate } from "../prompts/serde.js";
export type TypedPromptInputValues<RunInput> = InputValues<StringWithAutocomplete<Extract<keyof RunInput, string>>>;
export type Example = Record<string, string>;
/**
 * Input common to all prompt templates.
 */
export interface BasePromptTemplateInput<InputVariables extends InputValues = any, PartialVariableName extends string = any> {
    /**
     * A list of variable names the prompt template expects
     */
    inputVariables: Array<Extract<keyof InputVariables, string>>;
    /**
     * How to parse the output of calling an LLM on this formatted prompt
     */
    outputParser?: BaseOutputParser;
    /** Partial variables */
    partialVariables?: PartialValues<PartialVariableName>;
}
/**
 * Base class for prompt templates. Exposes a format method that returns a
 * string prompt given a set of input values.
 */
export declare abstract class BasePromptTemplate<RunInput extends InputValues = any, RunOutput extends BasePromptValueInterface = BasePromptValueInterface, PartialVariableName extends string = any> extends Runnable<RunInput, RunOutput> implements BasePromptTemplateInput {
    PromptValueReturnType: RunOutput;
    lc_serializable: boolean;
    lc_namespace: string[];
    get lc_attributes(): SerializedFields | undefined;
    inputVariables: Array<Extract<keyof RunInput, string>>;
    outputParser?: BaseOutputParser;
    partialVariables: PartialValues<PartialVariableName>;
    /**
     * Metadata to be used for tracing.
     */
    metadata?: Record<string, unknown>;
    /** Tags to be used for tracing. */
    tags?: string[];
    constructor(input: BasePromptTemplateInput);
    abstract partial(values: PartialValues): Promise<BasePromptTemplate<RunInput, RunOutput, PartialVariableName>>;
    /**
     * Merges partial variables and user variables.
     * @param userVariables The user variables to merge with the partial variables.
     * @returns A Promise that resolves to an object containing the merged variables.
     */
    mergePartialAndUserVariables(userVariables: TypedPromptInputValues<RunInput>): Promise<InputValues<Extract<keyof RunInput, string> | PartialVariableName>>;
    /**
     * Invokes the prompt template with the given input and options.
     * @param input The input to invoke the prompt template with.
     * @param options Optional configuration for the callback.
     * @returns A Promise that resolves to the output of the prompt template.
     */
    invoke(input: RunInput, options?: BaseCallbackConfig): Promise<RunOutput>;
    /**
     * Format the prompt given the input values.
     *
     * @param values - A dictionary of arguments to be passed to the prompt template.
     * @returns A formatted prompt string.
     *
     * @example
     * ```ts
     * prompt.format({ foo: "bar" });
     * ```
     */
    abstract format(values: TypedPromptInputValues<RunInput>): Promise<string>;
    /**
     * Format the prompt given the input values and return a formatted prompt value.
     * @param values
     * @returns A formatted PromptValue.
     */
    abstract formatPromptValue(values: TypedPromptInputValues<RunInput>): Promise<RunOutput>;
    /**
     * Return the string type key uniquely identifying this class of prompt template.
     */
    abstract _getPromptType(): string;
    /**
     * Return a json-like object representing this prompt template.
     * @deprecated
     */
    serialize(): SerializedBasePromptTemplate;
    /**
     * @deprecated
     * Load a prompt template from a json-like object describing it.
     *
     * @remarks
     * Deserializing needs to be async because templates (e.g. {@link FewShotPromptTemplate}) can
     * reference remote resources that we read asynchronously with a web
     * request.
     */
    static deserialize(data: SerializedBasePromptTemplate): Promise<BasePromptTemplate<InputValues, BasePromptValueInterface, string>>;
}
