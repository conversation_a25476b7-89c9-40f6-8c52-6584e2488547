import { Runnable } from "../runnables/base.js";
import type { InputValues } from "../utils/types/index.js";
import { TypedPromptInputValues } from "./base.js";
import { TemplateFormat } from "./template.js";
export declare class DictPromptTemplate<RunInput extends InputValues = InputValues, RunOutput extends Record<string, unknown> = Record<string, unknown>> extends Runnable<TypedPromptInputValues<RunInput>, RunOutput> {
    lc_namespace: string[];
    lc_serializable: boolean;
    template: Record<string, unknown>;
    templateFormat: TemplateFormat;
    inputVariables: Array<Extract<keyof RunInput, string>>;
    static lc_name(): string;
    constructor(fields: {
        template: Record<string, unknown>;
        templateFormat?: TemplateFormat;
    });
    format(values: TypedPromptInputValues<RunInput>): Promise<RunOutput>;
    invoke(values: TypedPromptInputValues<InputValues>): Promise<RunOutput>;
}
